import { ResumeSchema } from "./types";
import { defaultResumeData } from "@/app/store/defaults";
import { ChatMessage } from "@/app/store/types";

/**
 * 深度比较两个对象是否相等
 */
function deepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return obj1 === obj2;

  if (typeof obj1 !== typeof obj2) return false;

  if (typeof obj1 !== "object") return obj1 === obj2;

  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) return false;
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) return false;
    }
    return true;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}

/**
 * 深度合并两个对象，支持数组的智能合并
 */
function deepMerge(target: any, source: any): any {
  if (source == null || typeof source !== "object") {
    return source;
  }

  if (target == null || typeof target !== "object") {
    return source;
  }

  // 如果都是数组，直接使用source替换target
  if (Array.isArray(source)) {
    return [...source];
  }

  // 如果都是对象，进行深度合并
  const result = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (
        typeof source[key] === "object" &&
        source[key] !== null &&
        !Array.isArray(source[key])
      ) {
        // 递归合并嵌套对象
        result[key] = deepMerge(target[key], source[key]);
      } else {
        // 直接替换值（包括数组）
        result[key] = source[key];
      }
    }
  }

  return result;
}

/**
 * 智能合并AI响应的部分更新数据与完整的简历数据
 */
export function mergeResumeData(
  currentResumeData: ResumeSchema | null,
  partialUpdateData: Partial<ResumeSchema> | null
): ResumeSchema {
  // 如果没有当前数据，使用默认数据作为基础
  const baseData = currentResumeData || defaultResumeData;

  // 如果没有更新数据，返回当前数据
  if (!partialUpdateData) {
    return baseData;
  }

  // 深度合并数据
  return deepMerge(baseData, partialUpdateData) as ResumeSchema;
}

/**
 * 判断简历数据是否为默认数据
 */
export function isDefaultResumeData(resumeData: ResumeSchema | null): boolean {
  if (!resumeData) return true;
  return deepEqual(resumeData, defaultResumeData);
}

/**
 * 同步保存简历数据到服务端
 */
export async function syncResumeDataToServer(
  chatId: string | null,
  resumeData: ResumeSchema | null,
  newMessage?: ChatMessage
): Promise<boolean> {
  if (!chatId || !resumeData) return false;

  try {
    const body: { resumeData: ResumeSchema; newMessage?: ChatMessage } = {
      resumeData,
    };
    if (newMessage) {
      body.newMessage = newMessage;
    }

    const response = await fetch(`/api/chats/${chatId}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error("Failed to sync resume data to server");
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error syncing resume data to server:", error);
    return false;
  }
}

/**
 * 防抖函数，用于延迟执行数据同步
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 创建防抖的数据同步函数
 */
export const debouncedSyncResumeData = debounce(syncResumeDataToServer, 2000);
