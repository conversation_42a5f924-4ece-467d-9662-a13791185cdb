import { prisma } from "@/lib/database";

// 免费用户聊天次数限制
export const FREE_CHAT_LIMIT = 5;

// 会员相关类型定义
export interface MembershipStatus {
  isMember: boolean;
  chatCount: number;
  remainingChats: number;
  memberExpiry?: Date;
  canChat: boolean;
}

/**
 * 检查用户会员状态
 */
export async function checkMembershipStatus(
  userId: string
): Promise<MembershipStatus> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      chatCount: true,
      isMember: true,
      memberExpiry: true,
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  // 检查会员是否过期
  const now = new Date();
  const isActiveMember =
    user.isMember && user.memberExpiry && user.memberExpiry > now;

  // 计算剩余聊天次数
  const remainingChats = isActiveMember
    ? -1
    : Math.max(0, FREE_CHAT_LIMIT - user.chatCount);

  // 判断是否可以聊天
  const canChat = isActiveMember || user.chatCount < FREE_CHAT_LIMIT;

  return {
    isMember: isActiveMember!,
    chatCount: user.chatCount,
    remainingChats,
    memberExpiry: user.memberExpiry || undefined,
    canChat,
  };
}

/**
 * 增加用户聊天次数
 */
export async function incrementChatCount(userId: string): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: {
      chatCount: {
        increment: 1,
      },
    },
  });
}

/**
 * 激活用户会员
 */
export async function activateMembership(
  userId: string,
  days: number
): Promise<void> {
  const now = new Date();
  const expiry = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

  await prisma.user.update({
    where: { id: userId },
    data: {
      isMember: true,
      memberExpiry: expiry,
    },
  });
}

/**
 * 检查并更新过期会员状态
 */
export async function updateExpiredMemberships(): Promise<void> {
  const now = new Date();

  await prisma.user.updateMany({
    where: {
      isMember: true,
      memberExpiry: {
        lt: now,
      },
    },
    data: {
      isMember: false,
    },
  });
}

/**
 * 重置用户聊天次数（用于测试或管理员操作）
 */
export async function resetChatCount(userId: string): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: {
      chatCount: 0,
    },
  });
}
