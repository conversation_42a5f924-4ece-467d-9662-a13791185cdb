import Stripe from "stripe";
import { PaymentProvider } from "../provider";
import {
  PaymentMethod,
  CreateOrderParams,
  PaymentOrderResult,
  OrderStatusResult,
  CallbackVerifyResult,
  PaymentStatus,
  StripeConfig,
} from "../types";

/**
 * Stripe支付提供商
 */
export class StripeProvider extends PaymentProvider {
  private stripe: Stripe;
  private config: StripeConfig;

  constructor(config: StripeConfig) {
    super(PaymentMethod.STRIPE);
    this.config = config;

    // 创建Stripe实例
    this.stripe = new Stripe(config.secretKey, {
      apiVersion: (config.apiVersion as any) || "2023-10-16",
    });
  }

  async createOrder(params: CreateOrderParams): Promise<PaymentOrderResult> {
    try {
      // 创建Stripe支付意图
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(parseFloat(params.totalAmount) * 100), // 转换为分
        currency: "usd", // 默认使用美元，可以根据需要配置
        metadata: {
          out_trade_no: params.outTradeNo,
          subject: params.subject,
          body: params.body || "",
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      if (paymentIntent.client_secret) {
        return {
          success: true,
          payData: {
            client_secret: paymentIntent.client_secret,
            payment_intent_id: paymentIntent.id,
            publishable_key: this.config.publishableKey,
          },
          outTradeNo: params.outTradeNo,
        };
      }

      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: "创建Stripe支付意图失败",
      };
    } catch (error) {
      console.error("创建Stripe支付订单失败:", error);
      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: error instanceof Error ? error.message : "创建订单失败",
      };
    }
  }

  async queryOrderStatus(outTradeNo: string): Promise<OrderStatusResult> {
    try {
      // 通过metadata查找PaymentIntent
      const paymentIntents = await this.stripe.paymentIntents.list({
        limit: 10,
      });

      const paymentIntent = paymentIntents.data.find(
        (pi) => pi.metadata.out_trade_no === outTradeNo
      );

      if (!paymentIntent) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: "未找到对应的支付记录",
        };
      }

      let status: PaymentStatus;
      switch (paymentIntent.status) {
        case "succeeded":
          status = PaymentStatus.SUCCESS;
          break;
        case "canceled":
          status = PaymentStatus.CANCELLED;
          break;
        case "processing":
        case "requires_payment_method":
        case "requires_confirmation":
        case "requires_action":
          status = PaymentStatus.PENDING;
          break;
        case "requires_capture":
          status = PaymentStatus.PENDING;
          break;
        default:
          status = PaymentStatus.FAILED;
      }

      return {
        success: true,
        status,
        tradeNo: paymentIntent.id,
        paidAt:
          status === PaymentStatus.SUCCESS && paymentIntent.created
            ? new Date(paymentIntent.created * 1000)
            : undefined,
        rawData: paymentIntent,
      };
    } catch (error) {
      console.error("查询Stripe订单状态失败:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : "查询失败",
      };
    }
  }

  async verifyCallback(
    params: Record<string, any>
  ): Promise<CallbackVerifyResult> {
    try {
      // Stripe Webhook验证
      const sig = params.headers?.["stripe-signature"];
      const body = params.body;

      if (!sig || !body) {
        return {
          success: false,
          error: "缺少必要的验证参数",
        };
      }

      // 验证Webhook签名
      const event = this.stripe.webhooks.constructEvent(
        body,
        sig,
        this.config.webhookSecret
      );

      // 处理支付成功事件
      if (event.type === "payment_intent.succeeded") {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;

        return {
          success: true,
          outTradeNo: paymentIntent.metadata.out_trade_no,
          tradeNo: paymentIntent.id,
          status: PaymentStatus.SUCCESS,
          amount: (paymentIntent.amount / 100).toString(), // 转换为元
          rawData: event,
        };
      }

      // 处理支付失败事件
      if (event.type === "payment_intent.payment_failed") {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;

        return {
          success: true,
          outTradeNo: paymentIntent.metadata.out_trade_no,
          tradeNo: paymentIntent.id,
          status: PaymentStatus.FAILED,
          amount: (paymentIntent.amount / 100).toString(),
          rawData: event,
        };
      }

      // 处理支付取消事件
      if (event.type === "payment_intent.canceled") {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;

        return {
          success: true,
          outTradeNo: paymentIntent.metadata.out_trade_no,
          tradeNo: paymentIntent.id,
          status: PaymentStatus.CANCELLED,
          amount: (paymentIntent.amount / 100).toString(),
          rawData: event,
        };
      }

      return {
        success: false,
        error: `不支持的事件类型: ${event.type}`,
      };
    } catch (error) {
      console.error("验证Stripe回调失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    }
  }

  getDisplayName(): string {
    return "Stripe";
  }

  getIcon(): string {
    return "stripe";
  }

  isSupported(): boolean {
    return this.validateConfig();
  }

  getConfig(): StripeConfig {
    return this.config;
  }

  validateConfig(): boolean {
    return !!(
      this.config.secretKey &&
      this.config.publishableKey &&
      this.config.webhookSecret
    );
  }
}
