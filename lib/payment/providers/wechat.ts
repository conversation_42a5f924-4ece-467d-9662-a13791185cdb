// import { Payment } from "wechatpay-node-v3"; // 暂时禁用，等待兼容性修复
import { PaymentProvider } from "../provider";
import {
  PaymentMethod,
  CreateOrderParams,
  PaymentOrderResult,
  OrderStatusResult,
  CallbackVerifyResult,
  PaymentStatus,
  WechatPayConfig,
} from "../types";

/**
 * 微信支付提供商
 * 暂时禁用，等待 wechatpay-node-v3 包的兼容性修复
 */
export class WechatPayProvider extends PaymentProvider {
  // private payment: Payment; // 暂时注释
  private config: WechatPayConfig;

  constructor(config: WechatPayConfig) {
    super(PaymentMethod.WECHAT);
    this.config = config;

    // 暂时禁用微信支付实例创建
    // this.payment = new Payment({
    //   appid: config.appId,
    //   mchid: config.mchId,
    //   private_key: config.keyPath || "",
    //   serial_no: "",
    //   apiv3_private_key: config.apiKey,
    //   notify_url: "",
    // });
  }

  async createOrder(params: CreateOrderParams): Promise<PaymentOrderResult> {
    // 暂时禁用微信支付功能
    throw new Error("微信支付功能暂时不可用，等待依赖包兼容性修复");

    /* 原实现暂时注释
    try {
      // 微信支付统一下单参数
      const orderParams = {
        appid: this.config.appId,
        mchid: this.config.mchId,
        description: params.subject,
        out_trade_no: params.outTradeNo,
        notify_url: params.notifyUrl || "",
        amount: {
          total: Math.round(parseFloat(params.totalAmount) * 100), // 转换为分
          currency: "CNY",
        },
        scene_info: {
          payer_client_ip: "127.0.0.1", // 实际使用时应该获取真实IP
        },
      };

      // 创建Native支付订单（扫码支付）
      const result = await this.payment.native(orderParams);

      if (result.code_url) {
        return {
          success: true,
          payUrl: result.code_url, // 微信支付二维码链接
          payData: result,
          outTradeNo: params.outTradeNo,
        };
      }

      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: "创建微信支付订单失败",
      };
    } catch (error) {
      console.error("创建微信支付订单失败:", error);
      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: error instanceof Error ? error.message : "创建订单失败",
      };
    }
    */
  }

  async queryOrderStatus(outTradeNo: string): Promise<OrderStatusResult> {
    // 暂时禁用微信支付功能
    throw new Error("微信支付功能暂时不可用，等待依赖包兼容性修复");

    /* 原实现暂时注释
    try {
      // 查询订单状态
      const result = await this.payment.query({
        out_trade_no: outTradeNo,
      });

      if (result) {
        const { trade_state, transaction_id, success_time } = result;

        let status: PaymentStatus;
        switch (trade_state) {
          case "SUCCESS":
            status = PaymentStatus.SUCCESS;
            break;
          case "REFUND":
            status = PaymentStatus.REFUNDED;
            break;
          case "NOTPAY":
            status = PaymentStatus.PENDING;
            break;
          case "CLOSED":
            status = PaymentStatus.CANCELLED;
            break;
          case "REVOKED":
            status = PaymentStatus.CANCELLED;
            break;
          case "USERPAYING":
            status = PaymentStatus.PENDING;
            break;
          case "PAYERROR":
          default:
            status = PaymentStatus.FAILED;
        }

        return {
          success: true,
          status,
          tradeNo: transaction_id,
          paidAt: success_time ? new Date(success_time) : undefined,
          rawData: result,
        };
      }

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: "查询结果异常",
      };
    } catch (error) {
      console.error("查询微信支付订单状态失败:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : "查询失败",
      };
    }
    */
  }

  async verifyCallback(
    _params: Record<string, any>
  ): Promise<CallbackVerifyResult> {
    // 暂时禁用微信支付功能
    throw new Error("微信支付功能暂时不可用，等待依赖包兼容性修复");

    /* 原实现暂时注释
    try {
      // 微信支付回调验证
      // 注意：微信支付的回调验证比较复杂，需要验证签名
      // 这里简化处理，实际使用时需要完整的签名验证逻辑

      const { resource } = params;
      if (!resource) {
        return {
          success: false,
          error: "回调数据格式错误",
        };
      }

      // 解密回调数据（需要使用APIv3密钥）
      // 这里简化处理，实际需要解密resource.ciphertext
      const decryptedData = resource; // 实际应该是解密后的数据

      const outTradeNo = decryptedData.out_trade_no;
      const transactionId = decryptedData.transaction_id;
      const tradeState = decryptedData.trade_state;
      const totalAmount = decryptedData.amount?.total;

      let status: PaymentStatus;
      switch (tradeState) {
        case "SUCCESS":
          status = PaymentStatus.SUCCESS;
          break;
        case "REFUND":
          status = PaymentStatus.REFUNDED;
          break;
        case "CLOSED":
          status = PaymentStatus.CANCELLED;
          break;
        default:
          status = PaymentStatus.FAILED;
      }

      return {
        success: true,
        outTradeNo,
        tradeNo: transactionId,
        status,
        amount: totalAmount ? (totalAmount / 100).toString() : undefined, // 转换为元
        rawData: params,
      };
    } catch (error) {
      console.error("验证微信支付回调失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    }
    */
  }

  getDisplayName(): string {
    return "微信支付";
  }

  getIcon(): string {
    return "wechat";
  }

  isSupported(): boolean {
    // 暂时禁用微信支付功能
    return false;
  }

  getConfig(): WechatPayConfig {
    return this.config;
  }

  validateConfig(): boolean {
    // 暂时禁用微信支付功能
    return false;
  }
}
