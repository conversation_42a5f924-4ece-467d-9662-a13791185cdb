import {
  PaymentConfigs,
  AlipayConfig,
  WechatPayConfig,
  StripeConfig,
} from "./types";

/**
 * 支付配置管理
 */
export class PaymentConfigManager {
  private static instance: PaymentConfigManager;
  private configs: PaymentConfigs;

  private constructor() {
    this.configs = this.loadConfigs();
  }

  public static getInstance(): PaymentConfigManager {
    if (!PaymentConfigManager.instance) {
      PaymentConfigManager.instance = new PaymentConfigManager();
    }
    return PaymentConfigManager.instance;
  }

  private loadConfigs(): PaymentConfigs {
    return {
      alipay: {
        enabled: process.env.ALIPAY_ENABLED === "true",
        sandbox: process.env.NODE_ENV !== "production",
        appId: process.env.ALIPAY_APP_ID || "",
        privateKey: process.env.ALIPAY_PRIVATE_KEY || "",
        alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY || "",
        gateway: process.env.ALIPAY_GATEWAY || "https://openapi.alipaydev.com/gateway.do",
      },
      wechat: {
        enabled: process.env.WECHAT_PAY_ENABLED === "true",
        sandbox: process.env.NODE_ENV !== "production",
        appId: process.env.WECHAT_APP_ID || "",
        mchId: process.env.WECHAT_MCH_ID || "",
        apiKey: process.env.WECHAT_API_KEY || "",
        certPath: process.env.WECHAT_CERT_PATH,
        keyPath: process.env.WECHAT_KEY_PATH,
        apiUrl: process.env.WECHAT_API_URL || "https://api.mch.weixin.qq.com",
      },
      stripe: {
        enabled: process.env.STRIPE_ENABLED === "true",
        sandbox: process.env.NODE_ENV !== "production",
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || "",
        secretKey: process.env.STRIPE_SECRET_KEY || "",
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || "",
        apiVersion: process.env.STRIPE_API_VERSION || "2023-10-16",
      },
    };
  }

  public getAlipayConfig(): AlipayConfig {
    return this.configs.alipay;
  }

  public getWechatConfig(): WechatPayConfig {
    return this.configs.wechat;
  }

  public getStripeConfig(): StripeConfig {
    return this.configs.stripe;
  }

  public getAllConfigs(): PaymentConfigs {
    return this.configs;
  }

  public isPaymentMethodEnabled(method: string): boolean {
    switch (method.toLowerCase()) {
      case "alipay":
        return this.configs.alipay.enabled;
      case "wechat":
        return this.configs.wechat.enabled;
      case "stripe":
        return this.configs.stripe.enabled;
      default:
        return false;
    }
  }

  public getEnabledPaymentMethods(): string[] {
    const enabled: string[] = [];
    
    if (this.configs.alipay.enabled) {
      enabled.push("ALIPAY");
    }
    if (this.configs.wechat.enabled) {
      enabled.push("WECHAT");
    }
    if (this.configs.stripe.enabled) {
      enabled.push("STRIPE");
    }
    
    return enabled;
  }

  public validateConfigs(): { [key: string]: boolean } {
    return {
      alipay: this.validateAlipayConfig(),
      wechat: this.validateWechatConfig(),
      stripe: this.validateStripeConfig(),
    };
  }

  private validateAlipayConfig(): boolean {
    const config = this.configs.alipay;
    return !!(
      config.appId &&
      config.privateKey &&
      config.alipayPublicKey &&
      config.gateway
    );
  }

  private validateWechatConfig(): boolean {
    const config = this.configs.wechat;
    return !!(
      config.appId &&
      config.mchId &&
      config.apiKey &&
      config.apiUrl
    );
  }

  private validateStripeConfig(): boolean {
    const config = this.configs.stripe;
    return !!(
      config.secretKey &&
      config.publishableKey &&
      config.webhookSecret
    );
  }
}

// 导出单例实例
export const paymentConfig = PaymentConfigManager.getInstance();
