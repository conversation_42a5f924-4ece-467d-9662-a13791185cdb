import {
  CreateOrderParams,
  PaymentOrderResult,
  OrderStatusResult,
  CallbackVerifyResult,
  PaymentMethod,
} from "./types";

/**
 * 支付提供商基础接口
 * 所有支付方式都需要实现这个接口
 */
export abstract class PaymentProvider {
  protected method: PaymentMethod;

  constructor(method: PaymentMethod) {
    this.method = method;
  }

  /**
   * 获取支付方式
   */
  getMethod(): PaymentMethod {
    return this.method;
  }

  /**
   * 创建支付订单
   * @param params 订单参数
   * @returns 支付订单结果
   */
  abstract createOrder(params: CreateOrderParams): Promise<PaymentOrderResult>;

  /**
   * 查询订单状态
   * @param outTradeNo 商户订单号
   * @returns 订单状态结果
   */
  abstract queryOrderStatus(outTradeNo: string): Promise<OrderStatusResult>;

  /**
   * 验证支付回调
   * @param params 回调参数
   * @returns 验证结果
   */
  abstract verifyCallback(params: Record<string, any>): Promise<CallbackVerifyResult>;

  /**
   * 获取支付方式显示名称
   */
  abstract getDisplayName(): string;

  /**
   * 获取支付方式图标
   */
  abstract getIcon(): string;

  /**
   * 是否支持当前环境
   */
  abstract isSupported(): boolean;

  /**
   * 获取支付配置
   */
  abstract getConfig(): any;

  /**
   * 验证配置是否有效
   */
  abstract validateConfig(): boolean;
}

/**
 * 支付提供商工厂接口
 */
export interface PaymentProviderFactory {
  /**
   * 创建支付提供商实例
   * @param method 支付方式
   * @returns 支付提供商实例
   */
  createProvider(method: PaymentMethod): PaymentProvider;

  /**
   * 获取所有支持的支付方式
   * @returns 支付方式列表
   */
  getSupportedMethods(): PaymentMethod[];

  /**
   * 检查支付方式是否支持
   * @param method 支付方式
   * @returns 是否支持
   */
  isMethodSupported(method: PaymentMethod): boolean;
}
