import { StateCreator } from "zustand";
import { AppState } from "../types";
import { ResumeSchema } from "@/lib/types";

export const SECTION_ORDER = [
  "summary",
  "experience",
  "projects",
  "education",
  "skills",
];

export interface ResumeLayoutSlice {
  sectionOrder: string[];
  deleteSection: (sectionName: string) => void;
  moveSectionUp: (sectionName: string) => void;
  moveSectionDown: (sectionName: string) => void;
}

export const createResumeLayoutSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  ResumeLayoutSlice
> = (set) => ({
  sectionOrder: [...SECTION_ORDER],
  deleteSection: (sectionName) =>
    set((state) => {
      if (state.resumeData) {
        delete state.resumeData[sectionName as keyof ResumeSchema];
      }
    }),
  moveSectionUp: (sectionName) =>
    set((state) => {
      const currentIndex = state.sectionOrder.indexOf(sectionName);
      if (currentIndex > 0) {
        [
          state.sectionOrder[currentIndex - 1],
          state.sectionOrder[currentIndex],
        ] = [
          state.sectionOrder[currentIndex],
          state.sectionOrder[currentIndex - 1],
        ];
      }
    }),
  moveSectionDown: (sectionName) =>
    set((state) => {
      const currentIndex = state.sectionOrder.indexOf(sectionName);
      if (currentIndex >= 0 && currentIndex < state.sectionOrder.length - 1) {
        [
          state.sectionOrder[currentIndex],
          state.sectionOrder[currentIndex + 1],
        ] = [
          state.sectionOrder[currentIndex + 1],
          state.sectionOrder[currentIndex],
        ];
      }
    }),
});
