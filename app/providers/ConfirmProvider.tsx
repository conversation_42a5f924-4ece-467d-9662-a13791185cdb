"use client";

import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useCallback,
} from "react";
import { ConfirmDialog } from "@/app/components/common/ConfirmDialog";

// Define the shape of the options for the confirmation dialog
interface ConfirmOptions {
  title: string;
  description: string;
  confirmText: string;
  cancelText?: string;
  variant?: "default" | "destructive";
}

// Define the signature for the confirm function
// It takes the dialog options and the action to perform on confirmation
type ConfirmFunction = (options: ConfirmOptions, onConfirm: () => void) => void;

// Create the context
const ConfirmContext = createContext<ConfirmFunction | null>(null);

// The provider component that will wrap the application
export function ConfirmProvider({ children }: { children: ReactNode }) {
  const [options, setOptions] = useState<ConfirmOptions | null>(null);
  const [onConfirm, setOnConfirm] = useState<(() => void) | null>(null);

  // The function that child components will call to trigger the dialog
  const confirm = useCallback(
    (options: ConfirmOptions, onConfirmCallback: () => void) => {
      setOptions(options);
      // Store the callback function to be executed if the user confirms
      setOnConfirm(() => onConfirmCallback);
    },
    []
  );

  // This function is passed to the dialog, and it executes the stored callback
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    // Close the dialog by resetting the options
    setOptions(null);
  };

  // This function is passed to the dialog for cancellation
  const handleCancel = () => {
    setOptions(null);
  };

  return (
    <ConfirmContext.Provider value={confirm}>
      {children}
      {/* The dialog is only rendered when there are options set */}
      {options && (
        <ConfirmDialog
          open={!!options}
          onOpenChange={(open) => {
            if (!open) {
              setOptions(null);
            }
          }}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
          {...options}
        />
      )}
    </ConfirmContext.Provider>
  );
}

// The hook that components will use to access the confirm function
export function useConfirmDialog() {
  const context = useContext(ConfirmContext);
  if (!context) {
    throw new Error("useConfirmDialog must be used within a ConfirmProvider");
  }
  return { confirm: context };
}
