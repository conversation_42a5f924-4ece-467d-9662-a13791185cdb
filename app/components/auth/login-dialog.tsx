"use client";

import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Phone, Mail } from "lucide-react";
import { useTranslations } from "next-intl";
import { useErrorAlert } from "@/app/hooks/useErrorAlert";
import { useAuthForm } from "@/app/hooks/useAuthForm";

interface LoginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LoginDialog({ open, onOpenChange }: LoginDialogProps) {
  const t = useTranslations("Auth");
  const [loginMethod, setLoginMethod] = useState<"phone" | "google">("phone");
  const { showError } = useErrorAlert();
  const {
    phone,
    setPhone,
    code,
    setCode,
    isLoading,
    isSendingCode,
    codeSent,
    countdown,
    handleSendCode,
    handlePhoneLogin,
    resetForm,
  } = useAuthForm();

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open, resetForm]);

  const handleGoogleLogin = async () => {
    // setIsLoading(true); // isLoading is now managed by useAuthForm
    try {
      await signIn("google", { callbackUrl: "/" });
    } catch (error) {
      showError({
        title: t("loginError"),
        message: t("loginError"),
      });
      // setIsLoading(false); // isLoading is now managed by useAuthForm
    }
  };

  const handlePhoneLoginSubmit = async () => {
    const success = await handlePhoneLogin();
    if (success) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("loginTitle")}</DialogTitle>
          <DialogDescription>{t("loginDescription")}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Login Method Tabs */}
          <div className="flex space-x-2">
            <Button
              variant={loginMethod === "phone" ? "default" : "outline"}
              size="sm"
              onClick={() => setLoginMethod("phone")}
              className="flex-1"
            >
              <Phone className="w-4 h-4 mr-2" />
              {t("phoneLogin")}
            </Button>
            <Button
              variant={loginMethod === "google" ? "default" : "outline"}
              size="sm"
              onClick={() => setLoginMethod("google")}
              className="flex-1"
            >
              <Mail className="w-4 h-4 mr-2" />
              {t("googleLogin")}
            </Button>
          </div>

          {loginMethod === "phone" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder={t("phonePlaceholder")}
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  disabled={isLoading}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSendCode}
                  disabled={isSendingCode || countdown > 0 || !phone.trim()}
                  className="w-full"
                >
                  {isSendingCode
                    ? t("sendingCode")
                    : countdown > 0
                    ? `${t("resendCode")} (${countdown}s)`
                    : t("sendCode")}
                </Button>
              </div>

              {codeSent && (
                <div className="space-y-2">
                  <Input
                    placeholder={t("codePlaceholder")}
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    disabled={isLoading}
                  />
                  <Button
                    onClick={handlePhoneLoginSubmit}
                    disabled={isLoading || !code.trim()}
                    className="w-full"
                  >
                    {isLoading ? t("loggingIn") : t("login")}
                  </Button>
                </div>
              )}
            </div>
          )}

          {loginMethod === "google" && (
            <Button
              onClick={handleGoogleLogin}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? t("loggingIn") : t("continueWithGoogle")}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
