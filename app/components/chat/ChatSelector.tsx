"use client";

import { useState } from "react";
import { ChevronDown, MessageCircle, Trash2, Edit } from "lucide-react";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { useAppStore } from "@/app/store";
import { Chat } from "@/app/store/types";
import { useTranslations } from "next-intl";
import { ConfirmDialog } from "@/app/components/common/ConfirmDialog";
import { RenameChatDialog } from "./RenameChatDialog";

export const ChatSelector = () => {
  const t = useTranslations("DeleteConfirm");
  const {
    currentChat,
    chatList,
    isLoadingChats,
    loadChat,
    deleteChat,
    renameChat,
  } = useAppStore();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [dialogState, setDialogState] = useState<{
    type: "delete" | "rename" | null;
    chat: Chat | null;
  }>({ type: null, chat: null });

  const handleSelectChat = async (chat: Chat) => {
    if (chat.id !== currentChat?.id) {
      await loadChat(chat.id);
    }
    setIsMenuOpen(false);
  };

  const openDialog = (
    type: "delete" | "rename",
    chat: Chat,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    setDialogState({ type, chat });
  };

  const closeDialog = () => {
    setDialogState({ type: null, chat: null });
  };

  const handleConfirmDelete = async () => {
    if (dialogState.chat) {
      await deleteChat(dialogState.chat.id);
    }
  };

  const handleConfirmRename = async (newTitle: string) => {
    if (dialogState.chat) {
      return await renameChat(dialogState.chat.id, newTitle);
    }
    return false;
  };

  const displayTitle = currentChat?.title || "选择对话";
  const truncatedTitle =
    displayTitle.length > 20
      ? displayTitle.substring(0, 20) + "..."
      : displayTitle;

  return (
    <>
      <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-8 px-3 justify-between min-w-[120px] max-w-[200px]"
            disabled={isLoadingChats}
          >
            <span className="truncate">{truncatedTitle}</span>
            <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[300px]">
          {isLoadingChats ? (
            <DropdownMenuItem disabled>加载中...</DropdownMenuItem>
          ) : chatList.length === 0 ? (
            <DropdownMenuItem disabled>暂无对话</DropdownMenuItem>
          ) : (
            <>
              {chatList.map((chat) => (
                <DropdownMenuItem
                  key={chat.id}
                  className="group flex items-center justify-between p-3 cursor-pointer"
                  onClick={() => handleSelectChat(chat)}
                  onSelect={(e) => e.preventDefault()}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <MessageCircle className="h-4 w-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{chat.title}</div>
                    </div>
                  </div>
                  <div className="flex items-center opacity-0 group-hover:opacity-100 space-x-1.5">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 w-6"
                      onClick={(e) => openDialog("rename", chat, e)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 w-6"
                      onClick={(e) => openDialog("delete", chat, e)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </DropdownMenuItem>
              ))}
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmDialog
        open={dialogState.type === "delete"}
        onOpenChange={(isOpen) => !isOpen && closeDialog()}
        onConfirm={handleConfirmDelete}
        title={t("title")}
        description={t("description")}
        confirmText={t("confirmText")}
        cancelText={t("cancelText")}
        variant="destructive"
      />

      <RenameChatDialog
        isOpen={dialogState.type === "rename"}
        onClose={closeDialog}
        onRename={handleConfirmRename}
        currentTitle={dialogState.chat?.title || ""}
      />
    </>
  );
};
