"use client";

import { <PERSON><PERSON> } from "lucide-react";
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar";

export const TypingIndicator = () => {
  return (
    <div className="flex items-start gap-3">
      {/* <Avatar className="w-8 h-8">
        <AvatarFallback>
          <Bot className="w-4 h-4" />
        </AvatarFallback>
      </Avatar> */}
      <div className="rounded-lg px-3 py-2 max-w-[80%]">
        <div className="flex items-center space-x-1">
          {/* <span className="text-sm text-muted-foreground">AI正在思考</span> */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
          </div>
        </div>
      </div>
    </div>
  );
};
