"use client";

import { useState, KeyboardEvent, useRef, useEffect } from "react";
import { Textarea } from "@/app/components/ui/textarea";
import { Button } from "@/app/components/ui/button";
import { SendHorizontal, MessageCircle, X, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useAppStore } from "@/app/store";
import { Chat } from "@/app/store/types";
import { Badge } from "@/app/components/ui/badge";

interface ChatInputProps {
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  onSendMessage: (message: string, referencedChats?: string[]) => void;
}

export const ChatInput = ({
  input,
  setInput,
  isLoading,
  onSendMessage,
}: ChatInputProps) => {
  const t = useTranslations("ChatArea");
  const t_input = useTranslations("ChatInput");
  const { searchChatsForReference, chatList, currentChatId } = useAppStore();

  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<Chat[]>([]);
  const [referencedChats, setReferencedChats] = useState<Chat[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [cursorPosition, setCursorPosition] = useState(0);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 处理@引用功能
  const handleInputChangeWithReference = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;

    setInput(value);
    setCursorPosition(cursorPos);

    // 检查是否输入了@符号
    // const beforeCursor = value.substring(0, cursorPos);
    // const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    // if (atMatch) {
    //   const query = atMatch[1];
    //   setSearchQuery(query);
    //   setShowSuggestions(true);

    //   // 搜索对话
    //   if (query.length > 0) {
    //     searchChatsForReference(query).then((results) => {
    //       // 过滤掉当前对话和已引用的对话
    //       const filteredResults = results.filter(
    //         (chat) =>
    //           chat.id !== currentChatId &&
    //           !referencedChats.some((ref) => ref.id === chat.id)
    //       );
    //       setSuggestions(filteredResults);
    //     });
    //   } else {
    //     // 显示最近的对话
    //     const recentChats = chatList
    //       .filter(
    //         (chat) =>
    //           chat.id !== currentChatId &&
    //           !referencedChats.some((ref) => ref.id === chat.id)
    //       )
    //       .slice(0, 5);
    //     setSuggestions(recentChats);
    //   }
    // }
    // else {
    //   setShowSuggestions(false);
    //   setSuggestions([]);
    // }
  };

  // 选择引用对话
  const handleSelectReference = (chat: Chat) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const beforeCursor = input.substring(0, cursorPosition);
    const afterCursor = input.substring(cursorPosition);
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (atMatch) {
      const beforeAt = beforeCursor.substring(0, atMatch.index);
      const newInput = `${beforeAt}@${chat.title} ${afterCursor}`;

      // 更新输入值
      setInput(newInput);

      // 添加到引用列表
      setReferencedChats((prev) => [...prev, chat]);
      setShowSuggestions(false);
      setSuggestions([]);

      // 设置光标位置
      setTimeout(() => {
        const newCursorPos = beforeAt.length + chat.title.length + 2;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
      }, 0);
    }
  };

  // 移除引用
  const handleRemoveReference = (chatId: string) => {
    setReferencedChats((prev) => prev.filter((chat) => chat.id !== chatId));

    // 从输入文本中移除对应的@引用
    const chatToRemove = referencedChats.find((chat) => chat.id === chatId);
    if (chatToRemove) {
      const newInput = input.replace(
        new RegExp(`@${chatToRemove.title}\\s?`, "g"),
        ""
      );
      setInput(newInput);
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions && suggestions.length > 0) {
      if (event.key === "Escape") {
        setShowSuggestions(false);
        return;
      }
    }

    if (event.nativeEvent.isComposing || event.keyCode === 229) {
      return;
    }

    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      const syntheticEvent = {
        preventDefault: () => {},
      } as React.FormEvent<HTMLFormElement>;
      handleCustomSubmit(syntheticEvent);
    }
  };

  const handleCustomSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (input.trim() !== "") {
      const referencedChatIds = referencedChats.map((chat) => chat.id);
      onSendMessage(input, referencedChatIds);
      setInput("");
      setReferencedChats([]);
      setShowSuggestions(false);
    }
  };

  return (
    <div className="border-t bg-background p-4">
      {/* 引用的对话显示 */}
      {referencedChats.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {referencedChats.map((chat) => (
            <Badge
              key={chat.id}
              variant="secondary"
              className="flex items-center gap-1"
            >
              <MessageCircle className="w-3 h-3" />
              <span className="text-xs">{chat.title}</span>
              <button
                onClick={() => handleRemoveReference(chat.id)}
                className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* 建议列表 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="mb-3 max-h-32 overflow-y-auto border rounded-md bg-popover">
          {suggestions.map((chat) => (
            <button
              key={chat.id}
              onClick={() => handleSelectReference(chat)}
              className="w-full text-left px-3 py-2 hover:bg-accent text-sm border-b last:border-b-0"
            >
              <div className="flex items-center gap-2">
                <MessageCircle className="w-3 h-3 text-muted-foreground" />
                <span className="truncate">{chat.title}</span>
                <span className="text-xs text-muted-foreground ml-auto">
                  {chat._count?.messages || 0} 条消息
                </span>
              </div>
            </button>
          ))}
        </div>
      )}

      <form onSubmit={handleCustomSubmit} className="relative w-full">
        <Textarea
          ref={textareaRef}
          placeholder={t_input("placeholder")}
          value={input}
          onChange={handleInputChangeWithReference}
          onKeyDown={handleKeyDown}
          disabled={isLoading}
          rows={2}
          maxRows={4}
          className="resize-none pr-12 focus-visible:ring-1"
        />
        <Button
          type="submit"
          size="icon"
          className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full w-8 h-8"
          disabled={isLoading || !input.trim()}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <SendHorizontal className="w-4 h-4" />
          )}
        </Button>
      </form>
      <p className="text-xs text-muted-foreground text-right pt-2">
        {t_input("sendHint")}
      </p>
    </div>
  );
};
