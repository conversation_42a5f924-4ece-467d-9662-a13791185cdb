"use client";

import { useRef, useEffect } from "react";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { PenLine } from "lucide-react";
import { EnhancedMessageContent } from "@/app/components/chat/message-content";
import { TypingIndicator } from "./TypingIndicator";
import type { UIMessage } from "ai";

interface MessageListProps {
  messages: UIMessage[];
  isLoading: boolean;
}

export const MessageList = ({ messages, isLoading }: MessageListProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  return (
    <div className="flex-grow overflow-hidden">
      <ScrollArea className="h-full pr-1">
        <div className="space-y-4 py-4 px-6">
          {messages.map((message: UIMessage) => {
            if (message.role === "system") return null;
            return (
              <div
                key={message.id}
                className={`flex items-start gap-3 ${
                  message.role === "user" ? "justify-end" : ""
                }`}
              >
                <div
                  className={`rounded-lg px-3 py-2 max-w-[80%] ${
                    message.role === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  {/* 渲染消息的各个部分 */}
                  {message.parts.map((part, partIndex) => {
                    switch (part.type) {
                      case "text":
                        return (
                          <EnhancedMessageContent
                            key={partIndex}
                            content={part.text || ""}
                            enableMarkdown
                          />
                        );
                      case "tool-invocation":
                        return (
                          <div
                            key={partIndex}
                            className="m-1 text-xs text-muted-foreground"
                          >
                            <div className="flex items-center gap-1">
                              <PenLine size={16} />
                              <span>
                                {part.toolInvocation.state === "result"
                                  ? "已更新简历数据"
                                  : "正在更新简历..."}
                              </span>
                            </div>
                            {/* 更改详情 */}
                            {part.toolInvocation.state === "result" &&
                              part.toolInvocation.args && (
                                <pre className="mt-1 text-xs bg-muted/50 p-2 rounded">
                                  {JSON.stringify(
                                    part.toolInvocation.args,
                                    null,
                                    2
                                  )}
                                </pre>
                              )}
                          </div>
                        );
                    }
                  })}
                </div>
              </div>
            );
          })}
          {isLoading && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    </div>
  );
};
