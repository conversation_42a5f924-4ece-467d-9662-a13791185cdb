"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { useTranslations } from "next-intl";

interface RenameChatDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRename: (newTitle: string) => Promise<boolean>;
  currentTitle: string;
}

export const RenameChatDialog = ({
  isOpen,
  onClose,
  onRename,
  currentTitle,
}: RenameChatDialogProps) => {
  const t = useTranslations("ResumeNameDialog"); // Reusing translations
  const [newTitle, setNewTitle] = useState(currentTitle);
  const [isRenaming, setIsRenaming] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setNewTitle(currentTitle);
    }
  }, [isOpen, currentTitle]);

  const handleRename = async () => {
    if (!newTitle.trim() || newTitle === currentTitle) {
      onClose();
      return;
    }
    setIsRenaming(true);
    const success = await onRename(newTitle.trim());
    setIsRenaming(false);
    if (success) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
        </DialogHeader>
        <Input
          value={newTitle}
          onChange={(e) => setNewTitle(e.target.value)}
          placeholder={"placeholder"}
          onKeyDown={(e) => e.key === "Enter" && handleRename()}
        />
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">{t("cancel")}</Button>
          </DialogClose>
          <Button onClick={handleRename} disabled={isRenaming}>
            {isRenaming ? t("saving") : t("confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
