"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent } from "@/app/components/ui/card";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  Crown,
  RefreshCw,
} from "lucide-react";

interface PaymentPollingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
  onPaymentSuccess: () => void;
}

interface OrderStatus {
  orderId: string;
  status: string;
  amount: number;
  product: {
    name: string;
    description: string;
    duration: number;
  };
  paidAt?: string;
  isPaid: boolean;
}

export function PaymentPollingDialog({
  open,
  onOpenChange,
  orderId,
  onPaymentSuccess,
}: PaymentPollingDialogProps) {
  const [orderStatus, setOrderStatus] = useState<OrderStatus | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [timeLeft, setTimeLeft] = useState(30); // 5分钟 = 300秒
  const [isManualChecking, setIsManualChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 查询订单状态
  const checkOrderStatus = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch(`/api/orders/${orderId}/status`);
      const result = await response.json();

      if (result.success) {
        setOrderStatus(result.data);

        if (result.data.isPaid) {
          // 支付成功，停止轮询
          setIsPolling(false);
          onPaymentSuccess();
          return true;
        }
      } else {
        setError(result.error || "查询订单状态失败");
      }
      return false;
    } catch (error) {
      console.error("查询订单状态失败:", error);
      setError("网络错误，请稍后重试");
      return false;
    }
  }, [orderId, onPaymentSuccess]);

  // 手动确认支付完成
  const handleManualCheck = async () => {
    setIsManualChecking(true);
    const success = await checkOrderStatus();

    if (!success && orderStatus && !orderStatus.isPaid) {
      setError("未获取到支付结果，请确认是否已完成支付");
    }

    setIsManualChecking(false);
  };

  // 重置状态当弹窗打开时
  useEffect(() => {
    if (open && orderId) {
      // 重置所有状态
      setTimeLeft(30); // 重置为5分钟
      setIsPolling(true);
      setError(null);
      setOrderStatus(null);
      setIsManualChecking(false);
    }
  }, [open, orderId]);

  // 开始轮询
  useEffect(() => {
    if (!open || !orderId || !isPolling) return;

    // 立即检查一次
    checkOrderStatus();

    // 设置轮询间隔（每5秒查询一次）
    pollingIntervalRef.current = setInterval(() => {
      checkOrderStatus();
    }, 5000);

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [open, orderId, isPolling, checkOrderStatus]);

  // 单独处理倒计时
  useEffect(() => {
    if (!open || !isPolling) return;

    // 设置倒计时
    countdownIntervalRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setIsPolling(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [open, isPolling]);

  // 格式化倒计时显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // 支付成功状态
  if (orderStatus?.isPaid) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader className="text-center pb-4">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <DialogTitle className="text-lg">支付成功！</DialogTitle>
            <DialogDescription className="text-sm">
              恭喜您，会员已激活
            </DialogDescription>
          </DialogHeader>

          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-4 pb-4">
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Crown className="w-4 h-4 text-yellow-500" />
                  <span className="font-medium text-sm">
                    {orderStatus.product.name}
                  </span>
                </div>
                <div className="text-center text-xs text-muted-foreground space-y-1">
                  <p>支付金额：¥{orderStatus.amount}</p>
                  <p>会员时长：{orderStatus.product.duration}天</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Button
            onClick={() => onOpenChange(false)}
            className="w-full"
            size="sm"
          >
            开始使用会员服务
          </Button>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader className="text-center pb-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
            {isPolling ? (
              <Clock className="w-6 h-6 text-blue-600" />
            ) : (
              <AlertCircle className="w-6 h-6 text-orange-600" />
            )}
          </div>
          <DialogTitle className="text-lg">
            {isPolling ? "等待支付完成" : "支付确认"}
          </DialogTitle>
          <DialogDescription className="text-sm">
            {isPolling
              ? "请在新打开的页面完成支付，我们正在等待支付结果"
              : "未检查到支付结果，请手动确认支付状态"}
          </DialogDescription>
        </DialogHeader>

        {orderStatus && (
          <Card>
            <CardContent className="pt-4 pb-4">
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">商品</span>
                  <span className="font-medium">
                    {orderStatus.product.name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">金额</span>
                  <span className="font-medium">¥{orderStatus.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">时长</span>
                  <span className="font-medium">
                    {orderStatus.product.duration}天
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {error && (
          <div className="p-2 bg-red-50 border border-red-200 rounded-md">
            <p className="text-xs text-red-600">{error}</p>
          </div>
        )}

        <div className="space-y-2">
          {isPolling ? (
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">自动检测中...</span>
              </div>
              <p className="text-xs text-muted-foreground">
                剩余时间：{formatTime(timeLeft)}
              </p>
            </div>
          ) : (
            <Button
              onClick={handleManualCheck}
              disabled={isManualChecking}
              className="w-full"
              variant="default"
              size="sm"
            >
              {isManualChecking ? (
                <>
                  <Loader2 className="w-3 h-3 mr-2 animate-spin" />
                  检查中...
                </>
              ) : (
                <>
                  <RefreshCw className="w-3 h-3 mr-2" />
                  我已支付完成
                </>
              )}
            </Button>
          )}

          <Button
            onClick={() => onOpenChange(false)}
            variant="outline"
            className="w-full"
            size="sm"
          >
            稍后再说
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
