"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import {
  Crown,
  MessageCircle,
  Zap,
  Shield,
  Star,
  Check,
  Loader2,
} from "lucide-react";
import { useTranslations } from "next-intl";
import {
  useProducts,
  usePurchaseMembership,
  Product,
} from "@/app/hooks/useMembership";
import { PaymentPollingDialog } from "@/app/components/payment/payment-polling-dialog";
import { PaymentMethodSelector } from "@/app/components/payment/payment-method-selector";
import { ProductSelector } from "./ProductSelector";

interface MembershipDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPaymentSuccess: () => void;
  currentChatCount?: number;
  remainingChats?: number;
}

export function MembershipDialog({
  open,
  onOpenChange,
  onPaymentSuccess,
  currentChatCount = 0,
  remainingChats = 0,
}: MembershipDialogProps) {
  const t = useTranslations("Membership");
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>("ALIPAY");
  const [paymentOrderId, setPaymentOrderId] = useState<string | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  const { products, isLoading: productsLoading } = useProducts();
  const { purchaseMembership, isLoading: purchaseLoading } =
    usePurchaseMembership();

  // 当商品加载完成时，默认选中第一个商品
  useEffect(() => {
    if (products.length > 0 && !selectedProduct) {
      setSelectedProduct(products[0]);
    }
  }, [products, selectedProduct]);

  const handlePurchase = async () => {
    if (!selectedProduct) return;

    try {
      const result = await purchaseMembership(
        selectedProduct.id,
        selectedPaymentMethod
      );
      setPaymentOrderId(result.orderId);
      setShowPaymentDialog(true);
    } catch (error) {
      console.error("购买失败:", error);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false);
    setPaymentOrderId(null);
    onPaymentSuccess();
    onOpenChange(false);
  };

  const benefits = [
    {
      icon: MessageCircle,
      title: t("unlimitedChats"),
      description: t("unlimitedChatsDesc"),
    },
    {
      icon: Zap,
      title: t("priorityResponse"),
      description: t("priorityResponseDesc"),
    },
    {
      icon: Shield,
      title: t("advancedFeatures"),
      description: t("advancedFeaturesDesc"),
    },
    {
      icon: Star,
      title: t("exclusiveSupport"),
      description: t("exclusiveSupportDesc"),
    },
  ];

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-4xl max-h-[95vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-yellow-500" />
              {t("title")}
            </DialogTitle>
            <DialogDescription>{t("description")}</DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
              {/* 左侧：当前状态和会员权益 */}
              {/* <div className="lg:col-span-1 space-y-4"> */}
              {/* 当前使用情况 */}
              {/* <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">
                      {t("currentUsage")}
                    </CardTitle>
                    <CardDescription>默认5次免费聊天</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">已使用</span>
                        <Badge variant="secondary">{currentChatCount}/5</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">剩余次数</span>
                        <Badge variant="secondary">{remainingChats}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card> */}

              {/* 会员权益 */}
              {/* <div className="space-y-3">
                  <h3 className="font-medium text-sm">{t("memberBenefits")}</h3>
                  <div className="space-y-2">
                    {benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-md flex items-center justify-center">
                          <benefit.icon className="w-3 h-3 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1">
                            <h4 className="font-medium text-xs">
                              {benefit.title}
                            </h4>
                            <Check className="w-3 h-3 text-green-500" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div> */}
              {/* </div> */}

              {/* 右侧：商品选择 */}
              <div className="lg:col-span-2">
                <ProductSelector
                  products={products}
                  selectedProduct={selectedProduct}
                  setSelectedProduct={setSelectedProduct}
                  isLoading={productsLoading}
                />
              </div>
            </div>
          </div>

          {/* 支付方式选择 - 移到商品选择下方 */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <PaymentMethodSelector
              selectedMethod={selectedPaymentMethod}
              onMethodChange={setSelectedPaymentMethod}
            />
          </div>

          {/* 统一支付按钮 */}
          <div className="mt-6 space-y-4">
            <Button
              onClick={handlePurchase}
              disabled={purchaseLoading || !selectedProduct}
              className="w-full h-12 text-base font-medium"
              size="lg"
            >
              {purchaseLoading ? (
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
              ) : null}
              {selectedProduct
                ? `${t("payNow")} ¥${selectedProduct.price}`
                : t("pleaseSelectPackage")}
            </Button>

            {/* <p className="text-xs text-center text-muted-foreground">
              {t("securityNote")}
            </p> */}
          </div>
        </DialogContent>
      </Dialog>

      {/* 支付轮询弹窗 */}
      {paymentOrderId && (
        <PaymentPollingDialog
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
          orderId={paymentOrderId}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}
    </>
  );
}
