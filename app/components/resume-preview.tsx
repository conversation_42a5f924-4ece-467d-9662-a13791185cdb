"use client";

import { Card, CardContent } from "@/app/components/ui/card";
import { useAppStore } from "@/app/store";
import { ResumeSchema } from "@/lib/types";
import { Button } from "@/app/components/ui/button";
import { Download, FileText } from "lucide-react";
import { useTranslations } from "next-intl";
import { PersonalInfoSection } from "./resume/sections/PersonalInfoSection";
import { SummarySection } from "./resume/sections/SummarySection";
import { ExperienceSection } from "./resume/sections/ExperienceSection";
import { EducationSection } from "./resume/sections/EducationSection";
import { SkillsSection } from "./resume/sections/SkillsSection";
import { ProjectsSection } from "./resume/sections/ProjectsSection";

const ResumeContent = ({ resumeData }: { resumeData: ResumeSchema }) => {
  const { personalInfo, summary, experience, education, skills, projects } =
    resumeData;
  const { sectionOrder } = useAppStore();

  const renderSection = (sectionName: string, index: number) => {
    const isFirst = index === 0;
    const isLast = index === sectionOrder.length - 1;

    switch (sectionName) {
      case "summary":
        return (
          <SummarySection summary={summary} isFirst={isFirst} isLast={isLast} />
        );
      case "experience":
        return (
          <ExperienceSection
            experience={experience}
            isFirst={isFirst}
            isLast={isLast}
          />
        );
      case "education":
        return (
          <EducationSection
            education={education}
            isFirst={isFirst}
            isLast={isLast}
          />
        );
      case "skills":
        return (
          <SkillsSection skills={skills} isFirst={isFirst} isLast={isLast} />
        );
      case "projects":
        return (
          <ProjectsSection
            projects={projects}
            isFirst={isFirst}
            isLast={isLast}
          />
        );
      default:
        return null;
    }
  };

  return (
    <CardContent className="p-12 print:p-10 font-sans bg-white">
      <PersonalInfoSection personalInfo={personalInfo} />

      {/* Render sections dynamically based on order */}
      {sectionOrder.map((sectionName: string, index: number) => (
        <div key={sectionName}>{renderSection(sectionName, index)}</div>
      ))}
    </CardContent>
  );
};

export function ResumePreview() {
  const t = useTranslations("ResumePreview");
  const { resumeData, currentChat } = useAppStore();

  if (!resumeData) {
    return (
      <Card className="h-full flex border-0 items-center justify-center no-print">
        <CardContent className="text-center p-6">
          <p className="text-lg font-semibold">{t("placeholderTitle")}</p>
          <p className="text-muted-foreground mt-2">
            {t("placeholderSubtitle")}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="no-print h-full flex flex-col">
        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              <h2 className="text-lg font-semibold">{t("title")}</h2>
            </div>
            {/* {currentChat && (
              <div className="text-sm text-muted-foreground">
                当前对话: {currentChat.title}
              </div>
            )} */}
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => window.print()}
              variant="outline"
              size={"sm"}
            >
              <Download className="h-4 w-4" />
              {t("exportPdf")}
            </Button>
          </div>
        </div>
        <div className="flex-grow overflow-auto p-4 bg-gray-100">
          <div className="mx-auto w-[820px] shadow-lg">
            <ResumeContent resumeData={resumeData} />
          </div>
        </div>
      </div>
      <div className="print-only resume-print-area">
        <ResumeContent resumeData={resumeData} />
      </div>
    </div>
  );
}
