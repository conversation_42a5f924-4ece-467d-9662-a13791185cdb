"use client";

import { ReactNode, useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Trash2, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAppStore } from "@/app/store";
import { useDeleteConfirm } from "@/app/hooks/useDeleteConfirm";

interface ArrayItemWrapperProps {
  children: ReactNode;
  arrayPath: string;
  index: number;
  totalItems: number;
  className?: string;
  canDelete?: boolean;
  canMove?: boolean;
}

export function ArrayItemWrapper({
  children,
  arrayPath,
  index,
  totalItems,
  className,
  canDelete = true,
  canMove = true,
}: ArrayItemWrapperProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { confirmDelete } = useDeleteConfirm();
  const { deleteArrayItem, moveArrayItem } = useAppStore();

  const canMoveUp = canMove && index > 0;
  const canMoveDown = canMove && index < totalItems - 1;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    confirmDelete(() => {
      deleteArrayItem(arrayPath, index);
    });
  };

  const handleMoveUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveArrayItem(arrayPath, index, index - 1);
  };

  const handleMoveDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveArrayItem(arrayPath, index, index + 1);
  };

  return (
    <div
      className={cn("relative group", className)}
      onMouseEnter={(e) => {
        e.stopPropagation();
        setIsHovered(true);
      }}
      onMouseLeave={(e) => {
        e.stopPropagation();
        setIsHovered(false);
      }}
    >
      {children}

      {isHovered && (
        <div className="absolute top-1 right-1 flex gap-1 no-print opacity-0 group-hover:opacity-100 transition-opacity">
          {canMoveUp && (
            <Button
              size="sm"
              variant="outline"
              className="h-5 w-5 p-0 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleMoveUp}
            >
              <ChevronUp className="h-2.5 w-2.5" />
            </Button>
          )}

          {canMoveDown && (
            <Button
              size="sm"
              variant="outline"
              className="h-5 w-5 p-0 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleMoveDown}
            >
              <ChevronDown className="h-2.5 w-2.5" />
            </Button>
          )}

          {canDelete && (
            <Button
              size="sm"
              variant="outline"
              className="h-5 w-5 p-0 bg-white/90 hover:bg-red-50 hover:border-red-200 shadow-sm"
              onClick={handleDelete}
            >
              <Trash2 className="h-2.5 w-2.5 text-red-500" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
