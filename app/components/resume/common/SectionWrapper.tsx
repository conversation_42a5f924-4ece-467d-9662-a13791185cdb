"use client";

import { ReactNode, useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Trash2, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAppStore } from "@/app/store";
import { useDeleteConfirm } from "@/app/hooks/useDeleteConfirm";

interface SectionWrapperProps {
  children: ReactNode;
  sectionName: string;
  className?: string;
  canDelete?: boolean;
  canMoveUp?: boolean;
  canMoveDown?: boolean;
}

export function SectionWrapper({
  children,
  sectionName,
  className,
  canDelete = true,
  canMoveUp = false,
  canMoveDown = false,
}: SectionWrapperProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { deleteSection, moveSectionUp, moveSectionDown, sectionOrder } =
    useAppStore();
  const { confirmDelete } = useDeleteConfirm();

  // Calculate if section can move up/down based on current order
  const currentIndex = sectionOrder.indexOf(sectionName);
  const actualCanMoveUp = canMoveUp && currentIndex > 0;
  const actualCanMoveDown =
    canMoveDown && currentIndex < sectionOrder.length - 1;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    confirmDelete(() => {
      deleteSection(sectionName);
    });
  };

  const handleMoveUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveSectionUp(sectionName);
  };

  const handleMoveDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    moveSectionDown(sectionName);
  };

  return (
    <div
      className={cn("relative group mb-6 print:mb-4", className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}

      {/* Control buttons - only show on hover and not during print */}
      {isHovered && (
        <div className="absolute top-2 right-2 flex gap-1 no-print opacity-0 group-hover:opacity-100 transition-opacity">
          {actualCanMoveUp && (
            <Button
              size="sm"
              variant="outline"
              className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleMoveUp}
            >
              <ChevronUp className="h-3 w-3" />
            </Button>
          )}

          {actualCanMoveDown && (
            <Button
              size="sm"
              variant="outline"
              className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleMoveDown}
            >
              <ChevronDown className="h-3 w-3" />
            </Button>
          )}

          {canDelete && (
            <Button
              size="sm"
              variant="outline"
              className="h-6 w-6 p-0 bg-white/90 hover:bg-red-50 hover:border-red-200 shadow-sm"
              onClick={handleDelete}
            >
              <Trash2 className="h-3 w-3 text-red-500" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
