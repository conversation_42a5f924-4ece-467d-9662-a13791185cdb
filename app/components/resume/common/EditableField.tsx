"use client";

import { useAppStore } from "@/app/store";
import { AppState } from "@/app/store/types";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { HTMLAttributes, useState, useRef, useEffect } from "react";

interface EditableFieldProps extends HTMLAttributes<HTMLDivElement> {
  path: string;
  value: string;
  isTextArea?: boolean;
}

export function EditableField({
  path,
  value,
  isTextArea = false,
  className,
  ...props
}: EditableFieldProps) {
  const updateResumeField = useAppStore(
    (state: AppState) => state.updateResumeField
  );
  const [isEditing, setIsEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const t = useTranslations("EditableField");

  // This is the fix: A critical useEffect to sync the internal state
  // with the external prop `value`. This ensures that when the resume data
  // is updated by the AI, the EditableField reflects the latest data
  // when it enters editing mode.
  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  const handleBlur = () => {
    setIsEditing(false);
    // Only update the global state if the value has actually changed.
    if (currentValue !== value) {
      console.log(path, currentValue);

      updateResumeField(path, currentValue);
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (isEditing && isTextArea && textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [isEditing, isTextArea, currentValue]);

  return (
    <div
      {...props}
      className={cn(
        "hover:bg-muted/50 cursor-pointer p-1 rounded-md transition-colors",
        className
      )}
      onClick={() => setIsEditing(true)}
    >
      {isEditing ? (
        isTextArea ? (
          <textarea
            ref={textareaRef}
            value={currentValue}
            onChange={(e) => setCurrentValue(e.target.value)}
            onBlur={handleBlur}
            autoFocus
            className="bg-transparent outline-none w-full resize-none overflow-hidden"
            rows={1}
          />
        ) : (
          <input
            ref={inputRef}
            type="text"
            value={currentValue}
            onChange={(e) => setCurrentValue(e.target.value)}
            onBlur={handleBlur}
            autoFocus
            className="bg-transparent outline-none w-full"
          />
        )
      ) : (
        <span className={cn(!value && "text-muted-foreground/50")}>
          {value || t("clickToAdd")}
        </span>
      )}
    </div>
  );
}
