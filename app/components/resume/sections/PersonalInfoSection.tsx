"use client";

import { EditableField } from "@/app/components/resume/common/EditableField";
import { ResumeSchema } from "@/lib/types";

export const PersonalInfoSection = ({
  personalInfo,
}: {
  personalInfo: ResumeSchema["personalInfo"];
}) => (
  <div className="text-center mb-8 print:mb-6">
    <EditableField
      path="personalInfo.name"
      value={personalInfo.name || ""}
      className="text-3xl print:text-2xl font-bold tracking-tight w-full"
    />
    <div className="flex justify-center items-center gap-x-4 mt-2 resume-secondary-text">
      <EditableField
        path="personalInfo.email"
        value={personalInfo.email || ""}
      />
      <EditableField
        path="personalInfo.phone"
        value={personalInfo.phone || ""}
      />
      <EditableField
        path="personalInfo.linkedin"
        value={personalInfo.linkedin}
      />
    </div>
  </div>
);
