import { useTranslations } from "next-intl";
import { useConfirmDialog } from "@/app/providers/ConfirmProvider";

export function useErrorAlert() {
  const t = useTranslations("ErrorAlert");
  const { confirm } = useConfirmDialog();

  const showError = (options: { title?: string; message: string }) => {
    confirm(
      {
        title: options.title || t("title"),
        description: options.message,
        confirmText: t("confirm"),
        // We don't want a cancel button for a simple error alert
        cancelText: undefined,
        variant: "default",
      },
      () => {} // The user just clicks "OK", so the callback is empty.
    );
  };

  return { showError };
}