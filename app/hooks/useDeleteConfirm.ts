import { useTranslations } from "next-intl";
import { useConfirmDialog } from "@/app/providers/ConfirmProvider";

// This hook provides a streamlined way to show a standardized delete confirmation dialog.
export function useDeleteConfirm() {
  const t = useTranslations("DeleteConfirm");
  const { confirm } = useConfirmDialog();

  /**
   * Triggers the delete confirmation dialog.
   * @param onConfirm - The callback function to execute if the user confirms the deletion.
   * @param options - Optional overrides for the dialog's title and description.
   */
  const confirmDelete = (
    onConfirm: () => void,
    options?: {
      title?: string;
      description?: string;
    }
  ) => {
    return confirm(
      {
        title: options?.title || t("title"),
        description: options?.description || t("description"),
        confirmText: t("confirmText"),
        cancelText: t("cancelText"),
        variant: "destructive",
      },
      onConfirm // Pass the callback to the confirm function
    );
  };

  return { confirmDelete };
}