import { useTranslations } from "next-intl";
import { useConfirmDialog } from "@/app/providers/ConfirmProvider";

export function useLogoutConfirm() {
  const t = useTranslations("LogoutConfirm");
  const { confirm } = useConfirmDialog();

  const confirmLogout = (onConfirm: () => void) => {
    return confirm(
      {
        title: t("title"),
        description: t("description"),
        confirmText: t("confirm"),
        cancelText: t("cancel"),
        variant: "default",
      },
      onConfirm
    );
  };

  return { confirmLogout };
}