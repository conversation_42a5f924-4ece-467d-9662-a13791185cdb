"use client";

import { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import {
  MessageContent,
  EnhancedMessageContent,
} from "@/app/components/chat/message-content";

export default function TestChatPage() {
  const [testMessage, setTestMessage] = useState(`这是第一行文本
这是第二行文本

这是第四行文本（上面有一个空行）


这是第七行文本（上面有两个空行）

多行文本测试：
- 项目1
- 项目2
- 项目3

代码示例：
function hello() {
  console.log("Hello World");
}

结束。`);

  const [markdownMessage] = useState(`**粗体文本** 和 __另一种粗体__

*斜体文本* 和 _另一种斜体_

这是一些 \`代码\` 在行内。

普通文本
**混合** *格式* \`代码\` 在同一行

多行代码示例：
\`function hello() { return "world"; }\`

结束测试。`);

  const [messages, setMessages] = useState([
    {
      role: "assistant",
      content: "你好！我是AI助手。请输入一些包含换行的文本来测试换行解析功能。",
    },
    { role: "user", content: "测试换行\n第二行\n\n第四行" },
    { role: "assistant", content: testMessage },
    { role: "user", content: "测试Markdown格式" },
    { role: "assistant", content: markdownMessage, enhanced: true },
  ]);

  const [input, setInput] = useState("");

  const addTestMessage = () => {
    if (input.trim()) {
      setMessages((prev) => [...prev, { role: "user", content: input }]);
      setInput("");
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>聊天框换行解析测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 消息显示区域 */}
            <div className="border rounded-lg p-4 h-96 overflow-y-auto bg-gray-50">
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex items-start gap-3 ${
                      message.role === "user" ? "justify-end" : ""
                    }`}
                  >
                    <div
                      className={`rounded-lg px-3 py-2 max-w-[80%] ${
                        message.role === "user"
                          ? "bg-blue-500 text-white"
                          : "bg-white border"
                      }`}
                    >
                      {(message as any).enhanced ? (
                        <EnhancedMessageContent
                          content={message.content}
                          enableMarkdown={true}
                        />
                      ) : (
                        <MessageContent content={message.content} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 输入区域 */}
            <div className="space-y-2">
              <Textarea
                placeholder="输入包含换行的文本进行测试..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                rows={4}
                className="resize-none"
              />
              <div className="flex gap-2">
                <Button onClick={addTestMessage} disabled={!input.trim()}>
                  发送测试消息
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    setInput(
                      "第一行\n第二行\n\n第四行（上面有空行）\n\n\n第七行（上面有多个空行）"
                    )
                  }
                >
                  填入测试文本
                </Button>
              </div>
            </div>

            {/* 说明 */}
            <div className="text-sm text-gray-600 space-y-2">
              <p>
                <strong>测试说明：</strong>
              </p>
              <ul className="list-disc list-inside space-y-1">
                <li>输入包含换行符的文本</li>
                <li>空行会被正确显示</li>
                <li>多个连续空行也会被保留</li>
                <li>文本格式会被保持</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
