"use client";

import { AppHeader } from "@/app/components/app-header";
import { ChatArea } from "@/app/components/chat-area";
import { ResumePreview } from "@/app/components/resume-preview";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/app/components/ui/resizable";
import { useMembership } from "@/app/hooks/useMembership";
import { MembershipDialog } from "@/app/components/membership/membership-dialog";
import { useAppStore } from "@/app/store";

export default function Home() {
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const { isMembershipDialogOpen, closeMembershipDialog } = useAppStore();

  const handlePaymentSuccess = () => {
    // 支付成功后强制刷新会员状态
    refreshMembershipStatus(true);
  };

  return (
    <div className="h-screen flex flex-col">
      <AppHeader />
      <main className="flex-grow overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={35} minSize={25}>
            <ChatArea />
          </ResizablePanel>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={65} minSize={40}>
            <ResumePreview />
          </ResizablePanel>
        </ResizablePanelGroup>
      </main>
      <MembershipDialog
        open={isMembershipDialogOpen}
        onOpenChange={(open) => !open && closeMembershipDialog()}
        onPaymentSuccess={handlePaymentSuccess}
        currentChatCount={membershipStatus?.chatCount}
        remainingChats={membershipStatus?.remainingChats}
      />
    </div>
  );
}
