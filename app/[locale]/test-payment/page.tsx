"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { useSession } from "next-auth/react";

export default function TestPaymentPage() {
  const { data: session } = useSession();
  const [isCreating, setIsCreating] = useState(false);
  const [orderInfo, setOrderInfo] = useState<any>(null);

  const createTestOrder = async () => {
    if (!session) {
      alert("请先登录");
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch("/api/orders/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productId: "cm9ywqhqr0000tqjw8aqj8aqj", // 使用一个测试产品ID
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setOrderInfo(result.data);
          console.log("订单创建成功:", result.data);
        } else {
          alert("创建订单失败: " + result.error);
        }
      } else {
        alert("API请求失败: " + response.status);
      }
    } catch (error) {
      console.error("创建订单失败:", error);
      alert("创建订单失败");
    } finally {
      setIsCreating(false);
    }
  };

  const goToPay = () => {
    if (orderInfo?.payUrl) {
      window.open(orderInfo.payUrl, "_blank");
    }
  };

  const checkOrderStatus = async () => {
    if (!orderInfo?.orderId) return;

    try {
      const response = await fetch(`/api/orders/${orderInfo.orderId}/status`);
      if (response.ok) {
        const result = await response.json();
        console.log("订单状态:", result);
        alert(`订单状态: ${result.data?.status || "未知"}`);
      }
    } catch (error) {
      console.error("查询订单状态失败:", error);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>支付测试页面</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            当前用户: {session?.user?.email || "未登录"}
          </div>

          {!orderInfo ? (
            <Button 
              onClick={createTestOrder} 
              disabled={isCreating || !session}
              className="w-full"
            >
              {isCreating ? "创建中..." : "创建测试订单"}
            </Button>
          ) : (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div className="text-sm">
                  <span className="font-medium">订单ID:</span> {orderInfo.orderId}
                </div>
                <div className="text-sm">
                  <span className="font-medium">订单号:</span> {orderInfo.outTradeNo}
                </div>
                <div className="text-sm">
                  <span className="font-medium">金额:</span> ¥{orderInfo.amount}
                </div>
              </div>

              <div className="space-y-2">
                <Button onClick={goToPay} className="w-full">
                  去支付
                </Button>
                <Button onClick={checkOrderStatus} variant="outline" className="w-full">
                  查询订单状态
                </Button>
                <Button 
                  onClick={() => setOrderInfo(null)} 
                  variant="ghost" 
                  className="w-full"
                >
                  重新创建订单
                </Button>
              </div>
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            <p>测试流程:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>点击&quot;创建测试订单&quot;</li>
              <li>点击&quot;去支付&quot;跳转到支付宝</li>
              <li>在支付宝沙箱环境完成支付</li>
              <li>支付成功后会自动跳转回成功页面</li>
              <li>可以点击&quot;查询订单状态&quot;验证状态更新</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
