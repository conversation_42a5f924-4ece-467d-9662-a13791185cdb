import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/database";
import { paymentService, PaymentMethod } from "@/lib/payment";
import { generateOrderNo } from "@/lib/payment/providers/alipay";

export async function POST(request: NextRequest) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { productId, paymentMethod = "ALIPAY" } = body;

    if (!productId) {
      return Response.json(
        { error: "Product ID is required" },
        { status: 400 }
      );
    }

    // 验证支付方式
    const method = paymentMethod as PaymentMethod;
    if (!paymentService.isMethodSupported(method)) {
      return Response.json(
        { error: "Unsupported payment method" },
        { status: 400 }
      );
    }

    // 查询商品信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return Response.json({ error: "Product not found" }, { status: 404 });
    }

    if (!product.isActive) {
      return Response.json(
        { error: "Product is not available" },
        { status: 400 }
      );
    }

    // 从商品获取价格和时长
    const amount = Number(product.price);
    const duration = product.duration;

    // 生成订单号
    const outTradeNo = generateOrderNo();

    // 创建数据库订单记录
    const order = await prisma.order.create({
      data: {
        userId: session.user.id,
        productId: productId,
        amount: amount,
        duration: duration,
        outTradeNo: outTradeNo,
        status: "PENDING",
      },
    });

    // 创建支付订单
    const returnUrl = `${process.env.NEXTAUTH_URL}/zh/payment/success?orderId=${order.id}`;
    const notifyUrl = `${process.env.NEXTAUTH_URL}/api/payment/notify`;

    const paymentResult = await paymentService.createOrder(method, {
      outTradeNo: outTradeNo,
      totalAmount: amount.toString(),
      subject: `简历助手 - ${product.name}`,
      body: product.description || `购买${product.name}，享受无限制AI简历生成`,
      returnUrl: returnUrl,
      notifyUrl: notifyUrl,
    });

    if (!paymentResult.success) {
      // 如果支付订单创建失败，删除数据库订单
      await prisma.order.delete({
        where: { id: order.id },
      });

      return Response.json(
        { error: paymentResult.error || "Failed to create payment order" },
        { status: 500 }
      );
    }

    // 创建支付记录
    await prisma.payment.create({
      data: {
        orderId: order.id,
        amount: amount,
        method: method,
        status: "PENDING",
      },
    });

    return Response.json({
      success: true,
      data: {
        orderId: order.id,
        outTradeNo: outTradeNo,
        amount: amount,
        payUrl: paymentResult.payUrl,
        payData: paymentResult.payData, // 用于客户端SDK的支付数据
        paymentMethod: method,
      },
    });
  } catch (error) {
    console.error("Failed to create order:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
