import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/database";
import {
  paymentService,
  PaymentMethod,
  PaymentStatus,
} from "@/lib/payment";
import { activateMembership } from "@/lib/membership";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // 查询订单信息
    const order = await prisma.order.findUnique({
      where: {
        id: id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            description: true,
            price: true,
            duration: true,
          },
        },
        payments: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    if (!order) {
      return Response.json({ error: "Order not found" }, { status: 404 });
    }

    // 检查订单是否属于当前用户
    if (order.userId !== session.user.id) {
      return Response.json({ error: "Access denied" }, { status: 403 });
    }

    // 如果订单已经支付成功，直接返回状态
    if (order.status === "PAID") {
      return Response.json({
        success: true,
        data: {
          orderId: order.id,
          status: order.status,
          amount: order.amount,
          product: order.product,
          paidAt: order.paidAt,
          isPaid: true,
        },
      });
    }

    // 如果订单还在待支付状态，查询支付状态
    if (order.status === "PENDING") {
      try {
        // 获取支付方式（从支付记录中获取）
        const paymentMethod =
          (order.payments[0]?.method as PaymentMethod) || PaymentMethod.ALIPAY;

        const statusResult = await paymentService.queryOrderStatus(
          paymentMethod,
          order.outTradeNo
        );

        if (
          statusResult.success &&
          statusResult.status === PaymentStatus.SUCCESS
        ) {
          // 支付成功，更新本地订单状态
          await prisma.$transaction(async (tx) => {
            // 更新订单状态
            await tx.order.update({
              where: { id: order.id },
              data: {
                status: "PAID",
                tradeNo: statusResult.tradeNo,
                paidAt: statusResult.paidAt || new Date(),
              },
            });

            // 更新支付记录
            if (order.payments.length > 0) {
              await tx.payment.update({
                where: { id: order.payments[0].id },
                data: {
                  status: "SUCCESS",
                  tradeNo: statusResult.tradeNo,
                  rawData: statusResult.rawData as any,
                },
              });
            }
          });

          // 激活用户会员
          await activateMembership(order.userId, order.duration);

          return Response.json({
            success: true,
            data: {
              orderId: order.id,
              status: "PAID",
              amount: order.amount,
              product: order.product,
              paidAt: statusResult.paidAt || new Date(),
              isPaid: true,
            },
          });
        }
      } catch (error) {
        console.error(
          `查询${order.payments[0]?.method || "ALIPAY"}订单状态失败:`,
          error
        );
        // 继续返回本地状态，不因为查询失败而报错
      }
    }

    // 返回当前订单状态
    return Response.json({
      success: true,
      data: {
        orderId: order.id,
        status: order.status,
        amount: order.amount,
        product: order.product,
        paidAt: order.paidAt,
        isPaid: (order.status as string) === "PAID",
      },
    });
  } catch (error) {
    console.error("查询订单状态失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to query order status",
      },
      { status: 500 }
    );
  }
}
