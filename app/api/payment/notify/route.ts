import { NextRequest } from "next/server";
import { prisma } from "@/lib/database";
import {
  paymentService,
  PaymentMethod,
  PaymentStatus,
} from "@/lib/payment";
import { activateMembership } from "@/lib/membership";

// 根据回调参数判断支付方式
function determinePaymentMethod(params: Record<string, any>): PaymentMethod {
  // 支付宝回调包含特定字段
  if (params.out_trade_no && params.trade_status) {
    return PaymentMethod.ALIPAY;
  }

  // 微信支付回调包含特定字段
  if (params.resource || params.event_type) {
    return PaymentMethod.WECHAT;
  }

  // Stripe回调通过headers识别
  if (params.type && params.data) {
    return PaymentMethod.STRIPE;
  }

  // 默认返回支付宝（向后兼容）
  return PaymentMethod.ALIPAY;
}

// 通用支付回调处理逻辑
async function handlePaymentCallback(
  method: PaymentMethod,
  params: Record<string, any>
) {
  console.log(`处理${method}回调参数:`, params);

  // 验证回调签名
  const verifyResult = await paymentService.verifyCallback(method, params);

  if (!verifyResult.success) {
    console.error(`${method}回调签名验证失败:`, verifyResult.error);
    return { success: false, message: "签名验证失败" };
  }

  const { outTradeNo, tradeNo, status, amount } = verifyResult;

  if (!outTradeNo) {
    console.error("缺少订单号");
    return { success: false, message: "缺少订单号" };
  }

  // 查找订单
  const order = await prisma.order.findUnique({
    where: { outTradeNo },
    include: { user: true },
  });

  if (!order) {
    console.error("订单不存在:", outTradeNo);
    return { success: false, message: "订单不存在" };
  }

  // 检查订单状态
  if (order.status === "PAID") {
    console.log("订单已处理:", outTradeNo);
    return { success: true, message: "订单已处理" };
  }

  // 处理支付成功
  if (status === PaymentStatus.SUCCESS) {
    await prisma.$transaction(async (tx) => {
      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          status: "PAID",
          tradeNo: tradeNo,
          paidAt: new Date(),
        },
      });

      // 更新支付记录
      await tx.payment.updateMany({
        where: { orderId: order.id },
        data: {
          status: "SUCCESS",
          tradeNo: tradeNo,
          rawData: verifyResult.rawData,
        },
      });
    });

    // 激活用户会员
    await activateMembership(order.userId, order.duration);

    console.log("支付成功，已激活会员:", {
      userId: order.userId,
      orderId: order.id,
      duration: order.duration,
      method,
    });

    return { success: true, message: "支付成功" };
  }

  // 处理支付失败或取消
  if (status === PaymentStatus.FAILED || status === PaymentStatus.CANCELLED) {
    const orderStatus =
      status === PaymentStatus.CANCELLED ? "CANCELLED" : "EXPIRED";
    const paymentStatus =
      status === PaymentStatus.CANCELLED ? "CANCELLED" : "FAILED";

    await prisma.$transaction(async (tx) => {
      // 更新订单状态
      await tx.order.update({
        where: { id: order.id },
        data: {
          status: orderStatus,
          tradeNo: tradeNo,
        },
      });

      // 更新支付记录
      await tx.payment.updateMany({
        where: { orderId: order.id },
        data: {
          status: paymentStatus,
          tradeNo: tradeNo,
          rawData: verifyResult.rawData,
        },
      });
    });

    console.log(
      `支付${status === PaymentStatus.CANCELLED ? "已取消" : "失败"}:`,
      outTradeNo
    );
    return {
      success: true,
      message: `支付${status === PaymentStatus.CANCELLED ? "已取消" : "失败"}`,
    };
  }

  console.log("未处理的支付状态:", status);
  return { success: true, message: "状态已记录" };
}

// 处理GET请求（同步回调，主要用于支付宝）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params: Record<string, string> = {};

    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }

    // 根据参数判断支付方式，默认为支付宝
    const method = determinePaymentMethod(params);
    console.log(`收到${method} GET回调请求`);

    const result = await handlePaymentCallback(method, params);

    if (result.success) {
      // 重定向到支付成功页面
      const outTradeNo = params.out_trade_no;
      if (outTradeNo) {
        // 根据outTradeNo查找订单ID
        const order = await prisma.order.findUnique({
          where: { outTradeNo },
          select: { id: true },
        });

        if (order) {
          return Response.redirect(
            `${process.env.NEXTAUTH_URL}/zh/payment/success?orderId=${order.id}`
          );
        }
      }
      return new Response("success");
    } else {
      return new Response("fail", { status: 400 });
    }
  } catch (error) {
    console.error("处理支付宝GET回调失败:", error);
    return new Response("fail", { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取请求头，用于Stripe等需要特殊处理的支付方式
    const headers = Object.fromEntries(request.headers.entries());

    let params: Record<string, any> = {};
    let method: PaymentMethod;

    // 根据Content-Type判断如何解析请求体
    const contentType = request.headers.get("content-type") || "";

    if (contentType.includes("application/json")) {
      // JSON格式，通常用于Stripe
      const body = await request.text();
      params = { body, headers };
      method = PaymentMethod.STRIPE;
    } else {
      // Form格式，通常用于支付宝和微信
      const formData = await request.formData();
      for (const [key, value] of formData.entries()) {
        params[key] = value.toString();
      }
      method = determinePaymentMethod(params);
    }

    console.log(`收到${method} POST回调请求`);
    const result = await handlePaymentCallback(method, params);

    if (result.success) {
      return new Response("success");
    } else {
      return new Response("fail", { status: 400 });
    }
  } catch (error) {
    console.error("处理支付宝POST回调失败:", error);
    return new Response("fail", { status: 500 });
  }
}
