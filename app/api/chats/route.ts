import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/database";

// GET /api/chats - 获取用户的对话列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const chats = await prisma.chat.findMany({
      where: {
        userId: session.user.id,
        isDelete: false,
      },
      orderBy: {
        updatedAt: "desc",
      },
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    return Response.json({
      success: true,
      data: chats,
    });
  } catch (error) {
    console.error("Failed to load chats:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/chats - 创建新对话
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { title = "新对话", resumeData, resumeId } = body;

    const chat = await prisma.chat.create({
      data: {
        userId: session.user.id,
        title,
        resumeData: resumeData || null,
      },
      select: {
        id: true,
        title: true,
        resumeData: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      success: true,
      data: chat,
    });
  } catch (error) {
    console.error("Failed to create chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
