import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ResumeSchema } from "@/lib/types";
import { ChatMessage } from "@/app/store/types";

// GET /api/chats/[id] - 获取特定对话及其消息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    const chat = await prisma.chat.findUnique({
      where: {
        id,
        userId: session.user.id,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc",
          },
          select: {
            content: true,
            role: true,
            createdAt: true,
            parts: true,
            id: true,
          },
        },
      },
      omit: {
        isDelete: true,
        userId: true,
      },
    });

    if (!chat) {
      return Response.json({ error: "Chat not found" }, { status: 404 });
    }

    return Response.json({
      success: true,
      data: chat,
    });
  } catch (error) {
    console.error("Failed to load chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PATCH /api/chats/:id - 更新对话信息
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        {
          status: 401,
        }
      );
    }
    const { id } = await params;
    const body = await request.json();
    const {
      title,
      resumeData,
      newMessage,
    }: {
      title?: string;
      resumeData?: ResumeSchema;
      newMessage?: ChatMessage;
    } = body;

    // 验证对话是否属于当前用户
    const existingChat = await prisma.chat.findFirst({
      where: {
        id: id,
        userId: session.user.id,
      },
    });

    if (!existingChat) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }

    const updateData: any = {};
    if (title) {
      updateData.title = title;
    }
    if (resumeData) {
      updateData.resumeData = resumeData;
    }

    // 如果请求中包含了新消息（即用户手动更新简历产生的系统消息），则保存到数据库
    if (newMessage) {
      await prisma.chatMessage.create({
        data: {
          chatId: id,
          role: newMessage.role,
          content: newMessage.content,
        },
      });
    }

    const updatedChat = await prisma.chat.update({
      where: {
        id: id,
      },
      data: updateData,
    });

    return NextResponse.json({ data: updatedChat });
  } catch (error) {
    console.error("PATCH /api/chats/:id Error:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// DELETE /api/chats/[id] - 删除对话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    await prisma.chat.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        isDelete: true,
      },
    });

    return Response.json({
      success: true,
    });
  } catch (error) {
    console.error("Failed to delete chat:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
