import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/database";

// GET /api/chats/search - 搜索对话用于引用
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q") || "";

    if (!query.trim()) {
      return Response.json({
        success: true,
        data: [],
      });
    }

    const chats = await prisma.chat.findMany({
      where: {
        userId: session.user.id,
        isDelete: false,
        title: {
          contains: query,
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
      take: 10,
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      success: true,
      data: chats,
    });
  } catch (error) {
    console.error("Failed to search chats:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
