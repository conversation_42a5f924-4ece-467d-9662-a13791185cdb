import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { checkMembershipStatus } from "@/lib/membership";

export async function GET(request: NextRequest) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 获取会员状态
    const membershipStatus = await checkMembershipStatus(session.user.id);

    return Response.json({
      success: true,
      data: membershipStatus,
    });
  } catch (error) {
    console.error("Failed to get membership status:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
