import { NextRequest } from "next/server";
import { updateExpiredMemberships } from "@/lib/membership";

export async function GET(request: NextRequest) {
  try {
    // 验证请求来源（可以添加密钥验证）
    const authHeader = request.headers.get("authorization");
    const expectedToken = process.env.CRON_SECRET || "your-cron-secret";
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return Response.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 更新过期会员状态
    await updateExpiredMemberships();

    return Response.json({
      success: true,
      message: "Expired memberships updated successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Failed to update expired memberships:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// 支持POST方法（某些cron服务可能使用POST）
export async function POST(request: NextRequest) {
  return GET(request);
}
