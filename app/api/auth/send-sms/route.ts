import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/database"

// Mock SMS service - in production, use a real SMS service like Twilio, <PERSON>yun SMS, etc.
const sendSMS = async (phone: string, code: string) => {
  // Mock implementation - just log the code
  console.log(`SMS Code for ${phone}: ${code}`)
  // In production, integrate with SMS service:
  // await smsService.send(phone, `Your verification code is: ${code}`)
  return true
}

export async function POST(request: NextRequest) {
  try {
    const { phone } = await request.json()

    if (!phone) {
      return NextResponse.json({ error: "Phone number is required" }, { status: 400 })
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return NextResponse.json({ error: "Invalid phone number format" }, { status: 400 })
    }

    // Check rate limiting - only allow one SMS per minute per phone
    const recentVerification = await prisma.smsVerification.findFirst({
      where: {
        phone,
        createdAt: {
          gt: new Date(Date.now() - 60 * 1000) // 1 minute ago
        }
      }
    })

    if (recentVerification) {
      return NextResponse.json({ error: "Please wait before requesting another code" }, { status: 429 })
    }

    // Generate 6-digit code
    const code = Math.floor(100000 + Math.random() * 900000).toString()

    // Save verification code to database
    await prisma.smsVerification.create({
      data: {
        phone,
        code,
        expires: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
      }
    })

    // Send SMS
    const sent = await sendSMS(phone, code)

    if (!sent) {
      return NextResponse.json({ error: "Failed to send SMS" }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: "Verification code sent" })

  } catch (error) {
    console.error("SMS send error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
