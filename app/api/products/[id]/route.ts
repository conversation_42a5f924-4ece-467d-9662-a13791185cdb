import { NextRequest } from "next/server";
import { prisma } from "@/lib/database";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // 查询单个商品
    const product = await prisma.product.findUnique({
      where: {
        id: id,
      },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        duration: true,
        type: true,
        isActive: true,
        sortOrder: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!product) {
      return Response.json(
        {
          success: false,
          error: "Product not found",
        },
        { status: 404 }
      );
    }

    if (!product.isActive) {
      return Response.json(
        {
          success: false,
          error: "Product is not available",
        },
        { status: 400 }
      );
    }

    return Response.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error("获取商品详情失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to fetch product",
      },
      { status: 500 }
    );
  }
}
